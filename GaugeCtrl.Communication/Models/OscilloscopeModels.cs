using CommunityToolkit.Mvvm.ComponentModel;
using GaugeCtrl.Communication.Enums;

namespace GaugeCtrl.Communication.Models
{
    /// <summary>
    /// 示波器控制参数模型
    /// </summary>
    public partial class OscilloscopeControlParameter : ObservableObject
    {
        /// <summary>
        /// 示波器控制 - 向该参数写入1，会根据当前示波器通道的配置返回波形数据
        /// </summary>
        [ObservableProperty] private ushort _oscilloscopeControl;

        /// <summary>
        /// 示波器通道0的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel0Address;

        /// <summary>
        /// 示波器通道1的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel1Address;

        /// <summary>
        /// 示波器通道2的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel2Address;

        /// <summary>
        /// 示波器通道3的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel3Address;

        /// <summary>
        /// 示波器通道4的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel4Address;

        /// <summary>
        /// 示波器通道5的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel5Address;

        /// <summary>
        /// 示波器通道6的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel6Address;

        /// <summary>
        /// 示波器通道7的参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _channel7Address;
    }

    /// <summary>
    /// <summary>
    /// 示波器通道配置项
    /// </summary>
    public partial class OscilloscopeChannelConfig : ObservableObject
    {
        /// <summary>
        /// 通道编号 (0-7)
        /// </summary>
        [ObservableProperty] private int _channelNumber;

        /// <summary>
        /// 通道名称
        /// </summary>
        [ObservableProperty] private string _channelName = string.Empty;

        /// <summary>
        /// 参数地址映射
        /// </summary>
        [ObservableProperty] private ushort _parameterAddress;

        /// <summary>
        /// 是否启用该通道
        /// </summary>
        [ObservableProperty] private bool _isEnabled;

        /// <summary>
        /// 数据类型
        /// </summary>
        [ObservableProperty] private OscilloscopeDataType _dataType;

        /// <summary>
        /// 增益调节 (mv)
        /// </summary>
        [ObservableProperty] private ushort _gainAdjustment;

        /// <summary>
        /// 基线调节 (mv)
        /// </summary>
        [ObservableProperty] private ushort _baselineAdjustment;

        /// <summary>
        /// 参数类型描述
        /// </summary>
        [ObservableProperty] private string _description = string.Empty;
    }


    /// <summary>
    /// 示波器曲线数据上报模型
    /// </summary>
    public class OscilloscopeDataReport
    {
        /// <summary>
        /// 数据类型
        /// </summary>
        public OscilloscopeDataType DataType { get; set; }

        /// <summary>
        /// 8个通道
        /// </summary>
        public uint[] Channels { get; set; } = [8];

        /// <summary>
        /// 时间戳
        /// </summary>
        public ulong Timestamp { get; set; }
    }
}