using System.IO.Ports;
using NLog;
using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Communication.Models;
using GaugeCtrl.Communication.RequestInfos;

namespace GaugeCtrl.Communication
{
    /// <summary>
    /// 串口管理器
    /// 管理两个串口对象：USB2.0总线（参数配置和控制）和USB3.0总线（数据传输）
    /// </summary>
    public class SerialPortManager : IDisposable
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        private SerialPortService _usb2Port = new SerialPortService(); // USB2.0总线 - 用于参数配置和控制
        private SerialPortService _usb3Port = new SerialPortService(); // USB3.0总线 - 用于数据传输
        private bool _disposed = false;

        /// <summary>
        /// USB2.0串口配置
        /// </summary>
        public SerialPortConfig? Usb2Config { get; private set; }

        /// <summary>
        /// USB3.0串口配置
        /// </summary>
        public SerialPortConfig? Usb3Config { get; private set; }

        /// <summary>
        /// <summary>
        /// USB2.0串口是否已连接
        /// </summary>
        public bool IsUsb2Connected => _usb2Port?.IsConnected == true;

        /// <summary>
        /// USB3.0串口是否已连接
        /// </summary>
        public bool IsUsb3Connected => _usb3Port?.IsConnected == true;

        /// <summary>
        /// 两个串口是否都已连接
        /// </summary>
        public bool IsBothConnected => IsUsb2Connected && IsUsb3Connected;

        #region 事件定义

        /// <summary>
        /// USB2.0串口连接状态变化事件
        /// </summary>
        public event Action<bool>? Usb2ConnectionStatusChanged;

        /// <summary>
        /// USB3.0串口连接状态变化事件
        /// </summary>
        public event Action<bool>? Usb3ConnectionStatusChanged;
        

        #endregion

        /// <summary>
        /// 初始化串口管理器
        /// </summary>
        public SerialPortManager()
        {
            Logger.Info("串口管理器初始化");
        }

        /// <summary>
        /// 配置USB2.0串口（用于参数配置和控制）
        /// </summary>
        /// <param name="config">串口配置</param>
        public void ConfigureUsb2Port(SerialPortConfig config)
        {
            Usb2Config = config;
            Logger.Info($"USB2.0串口配置完成: {config.PortName}");
        }

        /// <summary>
        /// 配置USB3.0串口（用于数据传输）
        /// </summary>
        /// <param name="config">串口配置</param>
        public void ConfigureUsb3Port(SerialPortConfig config)
        {
            Usb3Config = config;
            Logger.Info($"USB3.0串口配置完成: {config.PortName}");
        }

        /// <summary>
        /// 连接USB2.0串口
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectUsb2Async()
        {
            try
            {
                if (Usb2Config == null)
                {
                    Logger.Error("USB2.0串口配置为空，无法连接");
                    return false;
                }
                // 订阅事件
                _usb2Port.ConnectionStatusChanged += OnUsb2ConnectionStatusChanged;


                var result = await _usb2Port.ConnectAsync(Usb2Config);
                Logger.Info($"USB2.0串口连接结果: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "连接USB2.0串口时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 连接USB3.0串口
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectUsb3Async()
        {
            try
            {
                if (Usb3Config == null)
                {
                    Logger.Error("USB3.0串口配置为空，无法连接");
                    return false;
                }

                _usb3Port = new SerialPortService();

                // 订阅事件
                _usb3Port.ConnectionStatusChanged += OnUsb3ConnectionStatusChanged;
                var result = await _usb3Port.ConnectAsync(Usb3Config);
                Logger.Info($"USB3.0串口连接结果: {result}");
                return result;
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "连接USB3.0串口时发生错误");
                return false;
            }
        }

        /// <summary>
        /// 连接所有串口
        /// </summary>
        /// <returns>连接结果</returns>
        public async Task<(bool usb2Success, bool usb3Success)> ConnectAllAsync()
        {
            var usb2Task = ConnectUsb2Async();
            var usb3Task = ConnectUsb3Async();

            var results = await Task.WhenAll(usb2Task, usb3Task);

            Logger.Info($"串口连接完成 - USB2.0: {results[0]}, USB3.0: {results[1]}");
            return (results[0], results[1]);
        }

        /// <summary>
        /// 断开USB2.0串口
        /// </summary>
        public async Task DisconnectUsb2Async()
        {
            if (_usb2Port != null)
            {
                await _usb2Port.DisconnectAsync();
                Logger.Info("USB2.0串口已断开");
            }
        }

        /// <summary>
        /// 断开USB3.0串口
        /// </summary>
        public async Task DisconnectUsb3Async()
        {
            if (_usb3Port != null)
            {
                await _usb3Port.DisconnectAsync();
                Logger.Info("USB3.0串口已断开");
            }
        }

        /// <summary>
        /// 断开所有串口
        /// </summary>
        public async Task DisconnectAllAsync()
        {
            var tasks = new List<Task>();

            if (_usb2Port != null)
                tasks.Add(DisconnectUsb2Async());

            if (_usb3Port != null)
                tasks.Add(DisconnectUsb3Async());

            await Task.WhenAll(tasks);
            Logger.Info("所有串口已断开");
        }

        /// <summary>
        /// 获取可用的串口列表
        /// </summary>
        /// <returns>串口名称列表</returns>
        public string[] GetAvailablePorts()
        {
            try
            {
                return SerialPort.GetPortNames();
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "获取可用串口列表时发生错误");
                return Array.Empty<string>();
            }
        }

        #region USB2.0串口命令发送方法（参数配置和控制）

        #region USB2.0串口命令发送方法（参数配置和控制）

        /// <summary>
        /// 发送授权命令（通过USB2.0）
        /// </summary>
        public async Task<bool> SendAuthCommandAsync()
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送授权命令");
                return false;
            }

            return await _usb2Port.SendAuthCommandAsync();
        }

        /// <summary>
        /// 发送设备控制命令（通过USB2.0）
        /// </summary>
        /// <param name="command">控制命令</param>
        public async Task<bool> SendDeviceControlCommandAsync(DeviceControlCommand command)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送设备控制命令");
                return false;
            }

            return await _usb2Port.SendDeviceControlCommandAsync(command);
        }

        /// <summary>
        /// 发送系统参数设置命令（通过USB2.0）
        /// </summary>
        /// <param name="parameter">系统参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendSystemParameterSetCommandAsync(SystemParameter parameter,
            bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送系统参数设置命令");
                return false;
            }

            return await _usb2Port.SendSystemParameterSetCommandAsync(parameter, isSingleDevice);
        }

        /// <summary>
        /// 发送滤波参数设置命令（通过USB2.0）
        /// </summary>
        /// <param name="parameter">滤波参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendFilterParameterSetCommandAsync(FilterParameter parameter,
            bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送滤波参数设置命令");
                return false;
            }

            return await _usb2Port.SendFilterParameterSetCommandAsync(parameter, isSingleDevice);
        }

        /// <summary>
        /// 发送时延参数设置命令（通过USB2.0）
        /// </summary>
        /// <param name="parameter">时延参数</param> 
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendDelayParameterSetCommandAsync(DelayParameter parameter, bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送时延参数设置命令");
                return false;
            }

            return await _usb2Port.SendDelayParameterSetCommandAsync(parameter, isSingleDevice);
        }

        /// <summary>
        /// 发送数据回传类型参数设置命令（通过USB2.0）
        /// </summary>
        /// <param name="parameter">数据类型参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendDataTypeParameterSetCommandAsync(RespTypeParameter parameter,
            bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送数据类型参数设置命令");
                return false;
            }

            return await _usb2Port.SendDataTypeParameterSetCommandAsync(parameter, isSingleDevice);
        }

        /// <summary>
        /// 发送触发阈值参数设置命令（通过USB2.0）
        /// </summary>
        /// <param name="parameter">阈值参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendThresholdParameterSetCommandAsync(ThresholdParameter parameter,
            bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送阈值参数设置命令");
                return false;
            }

            return await _usb2Port.SendThresholdParameterSetCommandAsync(parameter, isSingleDevice);
        }

        /// <summary>
        /// 发送信号提取参数设置命令（通过USB2.0）
        /// </summary>
        /// <param name="parameter">信号提取参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendSignalExtractionParameterSetCommandAsync(bool isUpload, bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送信号提取参数设置命令");
                return false;
            }

            return await _usb2Port.SendSignalExtractionParameterSetCommandAsync(isUpload, isSingleDevice);
        }

        /// <summary>
        /// 发送设备信息请求命令（通过USB2.0）
        /// </summary>
        /// <param name="deviceNumber">设备编号</param>
        /// <param name="parameterType">参数类型</param>
        public async Task<bool> SendDeviceInfoRequestAsync(byte deviceNumber, ParameterType parameterType)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送设备信息请求");
                return false;
            }

            return await _usb2Port.SendDeviceInfoRequestAsync(deviceNumber, parameterType);
        }

        /// <summary>
        /// 发送参数恢复出厂设置命令（通过USB2.0）
        /// </summary>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendFactoryResetCommandAsync(bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送出厂设置命令");
                return false;
            }

            return await _usb2Port.SendFactoryResetCommandAsync(isSingleDevice);
        }

        /// <summary>
        /// 发送示波器参数设置命令（通过USB2.0）
        /// </summary>
        /// <param name="parameter">示波器控制参数</param>
        /// <param name="isSingleDevice">是否为单个设备</param>
        public async Task<bool> SendOscilloscopeParameterSetCommandAsync(OscilloscopeControlParameter parameter,
            bool isSingleDevice = true)
        {
            if (_usb2Port?.IsConnected != true)
            {
                Logger.Warn("USB2.0串口未连接，无法发送示波器参数设置命令");
                return false;
            }

            return await _usb2Port.SendOscilloscopeParameterSetCommandAsync(parameter, isSingleDevice);
        }

        #endregion

        #region 事件处理方法

        public void SetUsb2EventHandlers(Action<SignalExtractionParams> signalExtractionParamsReceived)
        {
            _usb2Port.SignalExtractionParamsReceived += signalExtractionParamsReceived;
            // USB3.0主要用于原始数据传输，可以订阅原始数据事件
            // _usb3Port.SignalExtractionParamsReceived += OnUsb3RawDataReceived;
            // _usb3Port.OscilloscopeDataReceived += OnOscilloscopeDataReceived;
        }

        /// <summary>
        /// USB2.0连接状态变化处理
        /// </summary>
        private void OnUsb2ConnectionStatusChanged(bool isConnected)
        {
            Logger.Info($"USB2.0连接状态变化: {isConnected}");
            Usb2ConnectionStatusChanged?.Invoke(isConnected);
        }

        /// <summary>
        /// USB3.0连接状态变化处理
        /// </summary>
        private void OnUsb3ConnectionStatusChanged(bool isConnected)
        {
            Logger.Info($"USB3.0连接状态变化: {isConnected}");
            Usb3ConnectionStatusChanged?.Invoke(isConnected);
        }
        

        #endregion

        #endregion

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                try
                {
                    DisconnectAllAsync().Wait(2000);

                    _usb2Port?.Dispose();
                    _usb3Port?.Dispose();

                    Logger.Info("串口管理器资源已释放");
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, "释放串口管理器资源时发生错误");
                }
                finally
                {
                    _disposed = true;
                }
            }
        }
    }
}