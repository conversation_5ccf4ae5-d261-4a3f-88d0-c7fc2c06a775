using GaugeCtrl.Communication.Models;

namespace GaugeCtrl.Communication.Messages
{
    /// <summary>
    /// 系统参数接收消息
    /// </summary>
    public class SystemParameterReceivedMessage
    {
        public SystemParameter Parameter { get; }

        public SystemParameterReceivedMessage(SystemParameter parameter)
        {
            Parameter = parameter;
        }
    }

    /// <summary>
    /// 滤波参数接收消息
    /// </summary>
    public class FilterParameterReceivedMessage
    {
        public FilterParameter Parameter { get; }

        public FilterParameterReceivedMessage(FilterParameter parameter)
        {
            Parameter = parameter;
        }
    }

    /// <summary>
    /// 信号时延参数接收消息
    /// </summary>
    public class DelayParameterReceivedMessage
    {
        public DelayParameter Parameter { get; }

        public DelayParameterReceivedMessage(DelayParameter parameter)
        {
            Parameter = parameter;
        }
    }


    /// <summary>
    /// 触发阈值参数接收消息
    /// </summary>
    public class ThresholdParameterReceivedMessage
    {
        public ThresholdParameter Parameter { get; }

        public ThresholdParameterReceivedMessage(ThresholdParameter parameter)
        {
            Parameter = parameter;
        }
    }

    /// <summary>
    /// 信号提取参数接收消息
    /// </summary>
    public class SignalExtractionParameterReceivedMessage
    {
        public SignalExtractionParameter Parameter { get; }

        public SignalExtractionParameterReceivedMessage(SignalExtractionParameter parameter)
        {
            Parameter = parameter;
        }
    }
}