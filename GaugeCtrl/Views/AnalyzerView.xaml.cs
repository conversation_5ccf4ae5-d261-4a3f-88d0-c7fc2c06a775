using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using ScottPlot;
using ScottPlot.WPF;
using GaugeCtrl.ViewModels;

namespace GaugeCtrl.Views
{
    /// <summary>
    /// AnalyzerView.xaml 的交互逻辑
    /// </summary>
    public partial class AnalyzerView : UserControl
    {
        private AnalyzerViewModel? _viewModel;
        
        public AnalyzerView()
        {
            InitializeComponent();
            InitializePlots();
            this.DataContextChanged += AnalyzerView_DataContextChanged;
        }
        
        private void AnalyzerView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            _viewModel = DataContext as AnalyzerViewModel;
            _viewModel?.InitializeCountWpfPlot(CountsPlot);
        }
        
        private void InitializePlots()
        {
            // 初始化4个通道Plot
            InitializeChannelPlot(Plot1, "通道1", System.Windows.Media.Colors.Blue, false); // 隐藏X轴标签
            InitializeChannelPlot(Plot2, "通道2", System.Windows.Media.Colors.Red, false);   // 隐藏X轴标签
            InitializeChannelPlot(Plot3, "通道3", System.Windows.Media.Colors.Green, false); // 隐藏X轴标签
            InitializeChannelPlot(Plot4, "通道4", System.Windows.Media.Colors.Orange, true); // 显示X轴标签

            // 设置X轴同步
            SetupAxisSynchronization();


        }
        
        private void InitializeChannelPlot(WpfPlot plot, string title, System.Windows.Media.Color color, bool showXAxisLabels)
        {
            plot.Plot.Clear();

            // 设置轴标签
            if (showXAxisLabels)
            {
                plot.Plot.Axes.Bottom.Label.Text = "时间 (ms)";
                plot.Plot.Axes.Bottom.TickLabelStyle.IsVisible = true;
            }
            else
            {
                plot.Plot.Axes.Bottom.Label.Text = "";
                plot.Plot.Axes.Bottom.TickLabelStyle.IsVisible = false;
            }

            plot.Plot.Axes.Left.Label.Text = title;

            // 设置Y轴范围（12位ADC：0-4095）
            plot.Plot.Axes.SetLimitsY(0, 4095);

            // 设置X轴范围（0-100ms）
            plot.Plot.Axes.SetLimitsX(0, 100);
            
            //中文字体自适应
            plot.Plot.Font.Automatic();

            // 添加示例数据线
            double[] xs = new double[1000];
            double[] ys = new double[1000];

            for (int i = 0; i < 1000; i++)
            {
                xs[i] = i * 0.1; // 时间轴，每0.1ms一个点
                ys[i] = 2048 + 500 * Math.Sin(2 * Math.PI * i / 100 + GetChannelPhase(title)); // 不同通道不同相位
            }

            var scatter = plot.Plot.Add.Scatter(xs, ys);
            scatter.Color = ScottPlot.Color.FromColor(System.Drawing.Color.FromArgb(color.A, color.R, color.G, color.B));
            scatter.LineWidth = 1;
            scatter.MarkerSize = 0;
            scatter.LegendText = title;

            plot.Refresh();
        }

        private double GetChannelPhase(string title)
        {
            return title switch
            {
                "通道1" => 0,
                "通道2" => Math.PI / 4,
                "通道3" => Math.PI / 2,
                "通道4" => 3 * Math.PI / 4,
                _ => 0
            };
        }

        private void SetupAxisSynchronization()
        {
            // 设置X轴同步 - 通过鼠标事件来同步X轴
            var plots = new[] { Plot1, Plot2, Plot3, Plot4 };

            foreach (var plot in plots)
            {
                // 当鼠标在图表上操作时同步X轴
                plot.MouseMove += (sender, e) =>
                {
                    if (e.LeftButton == MouseButtonState.Pressed)
                    {
                        SynchronizeXAxis(plot, plots);
                    }
                };

                plot.MouseWheel += (sender, e) =>
                {
                    SynchronizeXAxis(plot, plots);
                };
            }
        }

        private void SynchronizeXAxis(WpfPlot sourcePlot, WpfPlot[] allPlots)
        {
            var xMin = sourcePlot.Plot.Axes.Bottom.Range.Min;
            var xMax = sourcePlot.Plot.Axes.Bottom.Range.Max;

            foreach (var plot in allPlots)
            {
                if (plot != sourcePlot)
                {
                    plot.Plot.Axes.SetLimitsX(xMin, xMax);
                    plot.Refresh();
                }
            }
        }


        
        private void AddSampleCountsData()
        {
            // 生成示例统计数据
            var random = new Random();
            var adcValues = new List<double>();
            var counts = new List<double>();
            
            // 生成A类型数据（正态分布）
            for (int i = 1; i <= 4095; i += 10)
            {
                var count = Math.Exp(-Math.Pow((i - 2048) / 500.0, 2)) * 1000 + random.NextDouble() * 10;
                if (count > 1)
                {
                    adcValues.Add(i);
                    counts.Add(count);
                }
            }
            
            if (adcValues.Count > 0)
            {
                var scatter = CountsPlot.Plot.Add.Scatter(adcValues.ToArray(), counts.ToArray());
                scatter.Color = ScottPlot.Colors.Blue;
                scatter.LineWidth = 0;
                scatter.MarkerSize = 3;
                scatter.LegendText = "A类型";
            }
            
            // 显示图例
            CountsPlot.Plot.Legend.IsVisible = true;
        }
        
        // 更新通道数据的方法
        public void UpdateChannelData(int channelIndex, double[] timeData, double[] valueData)
        {
            WpfPlot? targetPlot = channelIndex switch
            {
                0 => Plot1,
                1 => Plot2,
                2 => Plot3,
                3 => Plot4,
                _ => null
            };
            
            if (targetPlot == null || timeData.Length != valueData.Length)
                return;
                
            targetPlot.Plot.Clear();
            
            var scatter = targetPlot.Plot.Add.Scatter(timeData, valueData);
            scatter.LineWidth = 1;
            scatter.MarkerSize = 0;
            
            targetPlot.Plot.Axes.SetLimitsY(0, 4095);
            targetPlot.Refresh();
        }
        
        // 更新Counts数据的方法
        public void UpdateCountsData(double[] adcValues, double[] counts, string dataType)
        {
            if (adcValues.Length != counts.Length)
                return;
                
            CountsPlot.Plot.Clear();
            
            var color = dataType switch
            {
                "A" => ScottPlot.Colors.Blue,
                "H" => ScottPlot.Colors.Red,
                "W" => ScottPlot.Colors.Green,
                _ => ScottPlot.Colors.Blue
            };
            
            var scatter = CountsPlot.Plot.Add.Scatter(adcValues, counts);
            scatter.Color = color;
            scatter.LineWidth = 0;
            scatter.MarkerSize = 3;
            scatter.LegendText = $"{dataType}类型";
            
            CountsPlot.Plot.Axes.SetLimits(1, 4096, 1, counts.Max() * 1.1);
            CountsPlot.Plot.Legend.IsVisible = true;
            
            CountsPlot.Refresh();
        }
        
        // 清除所有图表数据
        public void ClearAllPlots()
        {
            Plot1.Plot.Clear();
            Plot2.Plot.Clear();
            Plot3.Plot.Clear();
            Plot4.Plot.Clear();
            CountsPlot.Plot.Clear();
            
            Plot1.Refresh();
            Plot2.Refresh();
            Plot3.Refresh();
            Plot4.Refresh();
            CountsPlot.Refresh();
        }
    }
}