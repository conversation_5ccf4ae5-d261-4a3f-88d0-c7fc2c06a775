<UserControl x:Class="GaugeCtrl.Views.AnalyzerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:GaugeCtrl.Views"
             xmlns:vm="clr-namespace:GaugeCtrl.ViewModels"
             xmlns:scottplot="clr-namespace:ScottPlot.WPF;assembly=ScottPlot.WPF"
             xmlns:c="http://schemas.superdev.ch/valueconverters/2016/xaml"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance vm:AnalyzerViewModel}">
    <UserControl.Resources>
        <!-- 转换器 -->
        <c:BoolToVisibilityConverter x:Key="BooleanToVisibilityConverter" FalseValue="Collapsed" TrueValue="Visible"/>
        <c:BoolToVisibilityConverter x:Key="BooleanToVisibilityInvertConverter" FalseValue="Visible" TrueValue="Collapsed"/>
        <c:BoolToFontWeightConverter x:Key="BooleanToFontWeightConverter" FalseValue="Normal" TrueValue="Bold" />


    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="3*" />
            <ColumnDefinition Width="5" />
            <ColumnDefinition Width="2*" />
        </Grid.ColumnDefinitions>

        <!-- 左侧4个Plot区域 - 垂直排列共用X轴 -->
        <Border BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" Grid.Column="0" Margin="10"
                Background="{DynamicResource RegionBrush}" CornerRadius="3">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!-- 示波器控制区域 -->
                <GroupBox Header="示波器数据采集控制" Grid.Row="0" Margin="5">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <ComboBox ItemsSource="{Binding DataTypeOptions}"
                                  SelectedItem="{Binding SelectedDataType}" Margin="2" Width="150" />
                        <Button Content="开始采集" Margin="5,0"
                                Visibility="{Binding OscilloscopeIsCollecting, Converter={StaticResource BooleanToVisibilityInvertConverter}}"
                                Command="{Binding OscilloscopeStartCommand}"
                                Style="{StaticResource ButtonPrimary}" hc:BorderElement.CornerRadius="3" />
                        <Button Content="停止采集" Margin="5,0"
                                Visibility="{Binding OscilloscopeIsCollecting, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Command="{Binding OscilloscopeStopCommand}"
                                Style="{StaticResource ButtonDanger}" hc:BorderElement.CornerRadius="3" />
                    </StackPanel>
                </GroupBox>

                <!-- 通道1 Plot (无X轴标签) -->
                <Border Grid.Row="1" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,0,0,1">
                    <scottplot:WpfPlot x:Name="Plot1" />
                </Border>

                <!-- 通道2 Plot (无X轴标签) -->
                <Border Grid.Row="2" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,0,0,1">
                    <scottplot:WpfPlot x:Name="Plot2" />
                </Border>

                <!-- 通道3 Plot (无X轴标签) -->
                <Border Grid.Row="3" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,0,0,1">
                    <scottplot:WpfPlot x:Name="Plot3" />
                </Border>

                <!-- 通道4 Plot (有X轴标签) -->
                <Border Grid.Row="4" BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0">
                    <scottplot:WpfPlot x:Name="Plot4" />
                </Border>
            </Grid>
        </Border>

        <!-- 分割条 -->
        <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="{DynamicResource BorderBrush}" />

        <!-- 右侧Counts Plot区域 -->
        <Border BorderBrush="{DynamicResource BorderBrush}" BorderThickness="1" Grid.Column="2" Margin="10"
                Background="{DynamicResource RegionBrush}" CornerRadius="3">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <scottplot:WpfPlot x:Name="CountsPlot" Grid.Row="1" Margin="5" />
                <GroupBox Header="通道(0~7)、显示类型(A/H/W)选择" Grid.Row="2" Margin="5">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <ComboBox ItemsSource="{Binding ChannelOptions}"
                                  SelectedIndex="{Binding SelectedCountsChannel}" Margin="2" Width="80" />
                        <ComboBox ItemsSource="{Binding DisplayTypeOptions}"
                                  SelectedItem="{Binding SelectedDisplayType}" Margin="2" Width="80" />
                        <Button Content="采集" Margin="5,0" Visibility="{Binding CountPlotIsCollecting, Converter={StaticResource BooleanToVisibilityInvertConverter}}"
                                Command="{Binding CountPlotStartCommand}"
                                Style="{StaticResource ButtonPrimary}" hc:BorderElement.CornerRadius="3" />
                        <Button Content="停止" Margin="5,0" Visibility="{Binding CountPlotIsCollecting, Converter={StaticResource BooleanToVisibilityConverter}}" Command="{Binding CountPlotStopCommand}"
                                Style="{StaticResource ButtonDanger}" hc:BorderElement.CornerRadius="3" />


                    </StackPanel>
                </GroupBox>
            </Grid>
        </Border>
    </Grid>
</UserControl>