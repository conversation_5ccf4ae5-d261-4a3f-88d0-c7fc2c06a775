<Window x:Class="GaugeCtrl.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        xmlns:local="clr-namespace:GaugeCtrl"
        xmlns:vm="clr-namespace:GaugeCtrl.ViewModels"
        xmlns:views="clr-namespace:GaugeCtrl.Views"
        xmlns:conv="clr-namespace:ValueConverters;assembly=ValueConverters"
        mc:Ignorable="d"
        Title="MainWindow" Height="768" Width="1366">
    <Window.Resources>
        <conv:BoolToBrushConverter x:Key="BoolToBrushConverter" TrueValue="Green" FalseValue="Red"></conv:BoolToBrushConverter>
        <conv:BoolToStringConverter x:Key="Usb2MsgConverter" TrueValue="USB2已连接" FalseValue="USB2已断开"></conv:BoolToStringConverter>
        <conv:BoolToStringConverter x:Key="Usb3MsgConverter" TrueValue="USB3已连接" FalseValue="USB3已断开"></conv:BoolToStringConverter>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <!-- 顶部导航栏 -->
        <Border Grid.Row="0" Background="{DynamicResource RegionBrush}" BorderBrush="{DynamicResource BorderBrush}"
                BorderThickness="0,0,0,1">
            <Grid Margin="10,5">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>

                <!-- 第一行：页面切换按钮和Button控件 -->
                <Grid Grid.Row="0" Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>

                    <!-- 左侧页面切换按钮 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <Button Content="⚙" ToolTip="串口设置" Margin="5,0" Padding="8,4" FontSize="14"
                                Command="{Binding SwitchToSerialSettingsCommand}"
                                Style="{StaticResource ButtonPrimary}" hc:BorderElement.CornerRadius="3" />
                        <Rectangle Width="1" Fill="{DynamicResource BorderBrush}" Margin="5,2" />
                        <Button Content="📊" ToolTip="分析仪" Margin="5,0" Padding="8,4" FontSize="14"
                                Command="{Binding SwitchToAnalyzerCommand}"
                                Style="{StaticResource ButtonPrimary}" hc:BorderElement.CornerRadius="3" />
                    </StackPanel>

                    <!-- 中间分析仪Button控件区域 (仅在分析仪页面显示) -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Left" Margin="20,0,0,0">
                        <StackPanel.Style>
                            <Style TargetType="StackPanel">
                                <Setter Property="Visibility" Value="Collapsed" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsAnalyzerViewActive}" Value="True">
                                        <Setter Property="Visibility" Value="Visible" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </StackPanel.Style>
                        <Rectangle Width="1" Fill="{DynamicResource BorderBrush}" Margin="0,2,10,2" />

                        <!-- 数据保存按钮 -->
                        <Button Content="保存原始数据" Margin="5,0" Command="{Binding AnalyzerViewModel.SaveRawDataCommand}"
                                Style="{StaticResource ButtonSuccess}" hc:BorderElement.CornerRadius="3" />
                        <Button Content="停止保存" Margin="5,0"
                                Command="{Binding AnalyzerViewModel.StopSaveRawDataCommand}"
                                Style="{StaticResource ButtonWarning}" hc:BorderElement.CornerRadius="3" />
                        <Button Content="保存提参后数据" Margin="5,0"
                                Command="{Binding AnalyzerViewModel.SaveProcessedDataCommand}"
                                Style="{StaticResource ButtonSuccess}" hc:BorderElement.CornerRadius="3" />
                        <Button Content="停止保存" Margin="5,0"
                                Command="{Binding AnalyzerViewModel.StopSaveProcessedDataCommand}"
                                Style="{StaticResource ButtonWarning}" hc:BorderElement.CornerRadius="3" />

                        <Rectangle Width="1" Fill="{DynamicResource BorderBrush}" Margin="10,2" />

                        <!-- 采集控制按钮 -->
                        <Button Content="开始采集" Margin="5,0"
                                Command="{Binding AnalyzerViewModel.StartCollectionCommand}"
                                Style="{StaticResource ButtonPrimary}" hc:BorderElement.CornerRadius="3" />
                        <Button Content="停止采集" Margin="5,0" Command="{Binding AnalyzerViewModel.StopCollectionCommand}"
                                Style="{StaticResource ButtonDanger}" hc:BorderElement.CornerRadius="3" />


                    </StackPanel>

                    <!-- 右侧连接状态 --> 
                    <StackPanel Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <Ellipse Width="12" Height="12" Margin="5,0"
                                 Fill="{Binding AnalyzerViewModel.Usb2Status,Converter={StaticResource BoolToBrushConverter}}">
                        </Ellipse>
                        <TextBlock
                            Text="{x:Bind ViewModel.AnalyzerViewModel.Usb2Status,Converter={StaticResource Usb2MsgConverter}}"
                            VerticalAlignment="Center"
                            Margin="5,0" FontSize="12" Foreground="{DynamicResource PrimaryTextBrush}" />
                        <Ellipse Width="12" Height="12" Margin="5,0"
                                 Fill="{Binding AnalyzerViewModel.Usb3Status,Converter={StaticResource BoolToBrushConverter}}">
                        </Ellipse>
                        <TextBlock
                            Text="{x:Bind ViewModel.AnalyzerViewModel.Usb3Status,Converter={StaticResource Usb3MsgConverter}}"
                            VerticalAlignment="Center"
                            Margin="5,0" FontSize="12" Foreground="{DynamicResource PrimaryTextBrush}" />
                    </StackPanel>
                </Grid>

                <!-- 第二行：设置控件 (仅在分析仪页面显示) -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left" Margin="0,5,0,0">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsAnalyzerViewActive}" Value="True">
                                    <Setter Property="Visibility" Value="Visible" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>

                    <!-- 触发设置 -->
                    <!-- <GroupBox Header="触发通道(CH0~7)" Margin="5,0">
                        <ComboBox Width="80" ItemsSource="{Binding AnalyzerViewModel.ChannelOptions}"
                                  SelectedItem="{Binding AnalyzerViewModel.SelectedTriggerChannel}" />
                    </GroupBox> -->
                    <GroupBox Header="触发阈值" Margin="5,0">
                        <hc:NumericUpDown Width="80" Value="{Binding AnalyzerViewModel.TriggerThreshold}"
                                          Minimum="0" Maximum="4095" />
                    </GroupBox>
                    <GroupBox Header="数据类型" Margin="5,0">
                        <ComboBox Width="100" ItemsSource="{Binding AnalyzerViewModel.DataTypeOptions}"
                                  SelectedItem="{Binding AnalyzerViewModel.SelectedDataType}" />
                    </GroupBox>

                    <Rectangle Width="1" Fill="{DynamicResource BorderBrush}" Margin="10,2" />

                    <!-- 通道设置 -->
                    <GroupBox Header="CH1显示" Margin="5,0">
                        <ComboBox ItemsSource="{Binding AnalyzerViewModel.ChannelOptions}"
                                  SelectedItem="{Binding AnalyzerViewModel.SelectedChannel1}" />
                    </GroupBox>
                    <GroupBox Header="CH2显示" Margin="5,0">
                        <ComboBox ItemsSource="{Binding AnalyzerViewModel.ChannelOptions}"
                                  SelectedItem="{Binding AnalyzerViewModel.SelectedChannel2}" />
                    </GroupBox>
                    <GroupBox Header="CH3显示" Margin="5,0">
                        <ComboBox ItemsSource="{Binding AnalyzerViewModel.ChannelOptions}"
                                  SelectedItem="{Binding AnalyzerViewModel.SelectedChannel3}" />
                    </GroupBox>
                    <GroupBox Header="CH4显示" Margin="5,0">
                        <ComboBox ItemsSource="{Binding AnalyzerViewModel.ChannelOptions}"
                                  SelectedItem="{Binding AnalyzerViewModel.SelectedChannel4}" />
                    </GroupBox>

                    <Rectangle Width="1" Fill="{DynamicResource BorderBrush}" Margin="10,2" />

                    <!-- 系统参数设置 -->
                    <GroupBox Header="系统参数设置" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <!-- 通道选择 -->
                            <TextBlock Text="通道:" VerticalAlignment="Center" Margin="2,0" />
                            <ComboBox ItemsSource="{Binding AnalyzerViewModel.ChannelOptions}"
                                      SelectedItem="{Binding AnalyzerViewModel.SelectedVadjChannel}" Width="80"
                                      Margin="2,0" />

                            <!-- Vadj设置 -->
                            <TextBlock Text="Vadj:" VerticalAlignment="Center" Margin="2,0" />
                            <hc:NumericUpDown Value="{Binding AnalyzerViewModel.VadjValue}"
                                              Minimum="0" Maximum="4095" Width="80" Margin="2,0" />

                            <!-- Voffset设置 -->
                            <TextBlock Text="Voffset:" VerticalAlignment="Center" Margin="2,0" />
                            <hc:NumericUpDown Value="{Binding AnalyzerViewModel.VoffsetValue}"
                                              Minimum="0" Maximum="4095" Width="80" Margin="2,0" />

                            <!-- 按钮 -->
                            <StackPanel Orientation="Horizontal"
                                        Margin="2,2,0,0">
                                <Button Content="读取" Command="{Binding AnalyzerViewModel.ReadSystemParamCommand}"
                                        Margin="2,0" Padding="8,2" FontSize="10" />
                                <Button Content="设置" Command="{Binding AnalyzerViewModel.SetSystemParamCommand}"
                                        Margin="2,0" Padding="8,2" FontSize="10" />
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 -->
        <ContentControl Grid.Row="1" Content="{Binding CurrentView}">
            <ContentControl.Resources>
                <DataTemplate DataType="{x:Type vm:SerialSettingsViewModel}">
                    <views:SerialSettingsView />
                </DataTemplate>
                <DataTemplate DataType="{x:Type vm:AnalyzerViewModel}">
                    <views:AnalyzerView />
                </DataTemplate>
            </ContentControl.Resources>
        </ContentControl>
    </Grid>
</Window>