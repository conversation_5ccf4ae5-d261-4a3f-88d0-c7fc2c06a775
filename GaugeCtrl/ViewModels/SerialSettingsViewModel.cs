using System.Collections.ObjectModel;
using System.IO;
using System.IO.Ports;
using System.Text.Json;
using System.Text.Json.Serialization;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using HandyControl.Controls;
using GaugeCtrl.Communication;
using GaugeCtrl.Communication.Messages;
using GaugeCtrl.Communication.Models;
using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Helpers;

namespace GaugeCtrl.ViewModels
{
    public partial class SerialSettingsViewModel : ObservableObject,
        IRecipient<SystemParameterReceivedMessage>,
        IRecipient<FilterParameterReceivedMessage>,
        IRecipient<DelayParameterReceivedMessage>
    {
        private readonly SerialPortManager _serialPortManager;
        private readonly string _settingsDirectory;
        private readonly string _configFilePath;
        

        
        [ObservableProperty]
        private ObservableCollection<string> _availableUsb2Ports = new();
        
        [ObservableProperty]
        private ObservableCollection<string> _availableUsb3Ports = new();
        
        [ObservableProperty]
        private ObservableCollection<int> _baudRates = new() { 9600, 19200, 38400, 57600, 115200, 230400, 460800, 921600 };
        
        [ObservableProperty]
        private ObservableCollection<int> _dataBits = new() { 5, 6, 7, 8 };
        
        [ObservableProperty]
        private ObservableCollection<Parity> _parityOptions = new()
        {
            Parity.None,
            Parity.Odd,
            Parity.Even,
            Parity.Mark,
            Parity.Space
        };
        
        [ObservableProperty]
        private ObservableCollection<StopBits> _stopBitsOptions = new()
        {
            StopBits.One,
            StopBits.OnePointFive,
            StopBits.Two
        };
        
        // USB2.0 串口配置
        [ObservableProperty]
        private string _selectedUsb2PortName = string.Empty;
        
        [ObservableProperty]
        private int _selectedUsb2BaudRate = 115200;
        
        [ObservableProperty]
        private int _selectedUsb2DataBits = 8;
        
        [ObservableProperty]
        private Parity _selectedUsb2Parity = Parity.None;
        
        [ObservableProperty]
        private StopBits _selectedUsb2StopBits = StopBits.One;
        
        [ObservableProperty]
        private bool _isUsb2Connected = false;
        
        // USB3.0 串口配置
        [ObservableProperty]
        private string _selectedUsb3PortName = string.Empty;
        
        [ObservableProperty]
        private int _selectedUsb3BaudRate = 921600;
        
        [ObservableProperty]
        private int _selectedUsb3DataBits = 8;
        
        [ObservableProperty]
        private Parity _selectedUsb3Parity = Parity.None;
        
        [ObservableProperty]
        private StopBits _selectedUsb3StopBits = StopBits.One;
        
        [ObservableProperty]
        private bool _isUsb3Connected = false;
        
        // 滤波参数设置
        [ObservableProperty]
        private ObservableCollection<KeyValuePair<FilterType, string>> _filterTypes = new()
        {
            new(FilterType.LowPass, "低通滤波"),
            new(FilterType.HighPass, "高通滤波"),
            new(FilterType.MovingAverage, "滑动平均")
        };
        
        [ObservableProperty]
        private FilterType _selectedFilterType = FilterType.LowPass;
        
        [ObservableProperty]
        private uint _lowPassCutoffFrequency = 1000;
        
        [ObservableProperty]
        private uint _lowPassSamplingPeriod = 100;
        
        [ObservableProperty]
        private uint _highPassCutoffFrequency = 100;
        
        [ObservableProperty]
        private uint _highPassSamplingPeriod = 100;
        
        [ObservableProperty]
        private uint _movingAverageWindowWidth = 10;
        
        [ObservableProperty]
        private bool _isLowPassVisible = true;
        
        [ObservableProperty]
        private bool _isHighPassVisible = false;
        
        [ObservableProperty]
        private bool _isMovingAverageVisible = false;
        
        // 信号时延参数设置
        [ObservableProperty]
        private uint _signalDelayTime = 100;
        
        // 系统参数设置
        [ObservableProperty]
        private ObservableCollection<KeyValuePair<ushort, string>> _systemParameterChannels = new()
        {
            new(0, "通道1"),
            new(1, "通道2"),
            new(2, "通道3"),
            new(3, "通道4"),
            new(4, "通道5"),
            new(5, "通道6"),
            new(6, "通道7"),
            new(7, "通道8")
        };

        [ObservableProperty]
        private ushort _selectedSystemParameterChannel = 0;

        [ObservableProperty]
        private ushort _gainAdjustment = 1000;

        [ObservableProperty]
        private ushort _baselineAdjustment = 0;

        // 示波器设置
        [ObservableProperty]
        private ObservableCollection<OscilloscopeChannelConfig> _oscilloscopeChannels = new();
        
        [ObservableProperty]
        private bool _isOscilloscopeEnabled = false;
        
        [ObservableProperty]
        private ObservableCollection<KeyValuePair<OscilloscopeDataType, string>> _oscilloscopeDataTypes = new()
        {
            new(OscilloscopeDataType.OriginalData, "原始数据"),
            new(OscilloscopeDataType.BaselineData, "基线数据"),
            new(OscilloscopeDataType.BitWidthData, "位宽数据"),
            new(OscilloscopeDataType.SignalHeightData, "信号高度数据"),
            new(OscilloscopeDataType.SignalWidthData, "信号宽度数据"),
            new(OscilloscopeDataType.SignalAreaData, "信号面积数据")
        };

        // 参数上报状态显示
        [ObservableProperty]
        private string _lastParameterUpdateInfo = "等待参数上报...";

        [ObservableProperty]
        private SystemParameter? _receivedSystemParameter;

        [ObservableProperty]
        private FilterParameter? _receivedFilterParameter;

        [ObservableProperty]
        private DelayParameter? _receivedDelayParameter;
        
        
        public bool IsConfigured { get; private set; }
        
        public SerialPortManager SerialPortManager => _serialPortManager;
        
        public SerialSettingsViewModel(SerialPortManager? serialPortManager = null)
        {
            _serialPortManager = serialPortManager ?? new SerialPortManager();
            
            // 订阅连接状态变化事件
            _serialPortManager.Usb2ConnectionStatusChanged += OnUsb2ConnectionStatusChanged;
            _serialPortManager.Usb3ConnectionStatusChanged += OnUsb3ConnectionStatusChanged;
            
            // 初始化配置文件路径
            _settingsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "settings");
            _configFilePath = Path.Combine(_settingsDirectory, "serial_config.json");
            
            // 确保settings目录存在
            if (!Directory.Exists(_settingsDirectory))
            {
                Directory.CreateDirectory(_settingsDirectory);
            }
            
            LoadConfigurationFromFile();
            RefreshPorts();
            
            // 初始化连接状态
            IsUsb2Connected = _serialPortManager.IsUsb2Connected;
            IsUsb3Connected = _serialPortManager.IsUsb3Connected;
            
            // 初始化滤波类型可见性
            UpdateFilterTypeVisibility();
            
            // 初始化示波器通道
            InitializeOscilloscopeChannels();
        }
        
        partial void OnSelectedFilterTypeChanged(FilterType value)
        {
            UpdateFilterTypeVisibility();
        }
        
        private void UpdateFilterTypeVisibility()
        {
            IsLowPassVisible = SelectedFilterType == FilterType.LowPass;
            IsHighPassVisible = SelectedFilterType == FilterType.HighPass;
            IsMovingAverageVisible = SelectedFilterType == FilterType.MovingAverage;
        }
        
        private void LoadConfigurationFromFile()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var jsonString = File.ReadAllText(_configFilePath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        Converters = { new JsonStringEnumConverter() }
                    };
                    var configs = JsonSerializer.Deserialize<Dictionary<string, SerialPortConfig>>(jsonString, options);
                    
                    if (configs != null)
                    {
                        if (configs.TryGetValue("usb2", out var usb2Config))
                        {
                            SelectedUsb2PortName = usb2Config.PortName;
                            SelectedUsb2BaudRate = usb2Config.BaudRate;
                            SelectedUsb2DataBits = usb2Config.DataBits;
                            SelectedUsb2Parity = usb2Config.Parity;
                            SelectedUsb2StopBits = usb2Config.StopBits;
                        }
                        
                        if (configs.TryGetValue("usb3", out var usb3Config))
                        {
                            SelectedUsb3PortName = usb3Config.PortName;
                            SelectedUsb3BaudRate = usb3Config.BaudRate;
                            SelectedUsb3DataBits = usb3Config.DataBits;
                            SelectedUsb3Parity = usb3Config.Parity;
                            SelectedUsb3StopBits = usb3Config.StopBits;
                        }
                    }
                }
                else
                {
                    // 使用默认配置
                    SetDefaultConfigurations();
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"加载配置文件失败", ex);
                SetDefaultConfigurations();
            }
        }
        
        private void SetDefaultConfigurations()
        {
            // USB2.0默认配置
            SelectedUsb2PortName = "COM1";
            SelectedUsb2BaudRate = 115200;
            SelectedUsb2DataBits = 8;
            SelectedUsb2Parity = Parity.None;
            SelectedUsb2StopBits = StopBits.One;
            
            // USB3.0默认配置
            SelectedUsb3PortName = "COM2";
            SelectedUsb3BaudRate = 921600;
            SelectedUsb3DataBits = 8;
            SelectedUsb3Parity = Parity.None;
            SelectedUsb3StopBits = StopBits.One;
        }
        
        [RelayCommand]
        private void RefreshPorts()
        {
            try
            {
                var ports = _serialPortManager.GetAvailablePorts();
                AvailableUsb2Ports.Clear();
                AvailableUsb3Ports.Clear();
                
                foreach (var port in ports)
                {
                    AvailableUsb2Ports.Add(port);
                    AvailableUsb3Ports.Add(port);
                }
                
                // 更新选择的端口
                UpdateSelectedPorts();
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"刷新串口列表失败", ex);
            }
        }
        
        private void UpdateSelectedPorts()
        {
            // 更新USB2.0端口选择
            if (!string.IsNullOrEmpty(SelectedUsb2PortName) && !AvailableUsb2Ports.Contains(SelectedUsb2PortName))
            {
                SelectedUsb2PortName = AvailableUsb2Ports.FirstOrDefault() ?? string.Empty;
            }
            else if (string.IsNullOrEmpty(SelectedUsb2PortName) && AvailableUsb2Ports.Count > 0)
            {
                SelectedUsb2PortName = AvailableUsb2Ports.First();
            }
            
            // 更新USB3.0端口选择
            if (!string.IsNullOrEmpty(SelectedUsb3PortName) && !AvailableUsb3Ports.Contains(SelectedUsb3PortName))
            {
                SelectedUsb3PortName = AvailableUsb3Ports.Skip(1).FirstOrDefault() ?? AvailableUsb3Ports.FirstOrDefault() ?? string.Empty;
            }
            else if (string.IsNullOrEmpty(SelectedUsb3PortName) && AvailableUsb3Ports.Count > 1)
            {
                SelectedUsb3PortName = AvailableUsb3Ports.Skip(1).First();
            }
        }
        
        [RelayCommand]
        private async Task ConnectUsb2()
        {
            if (ValidateUsb2Input())
            {
                var config = SerialPortConfig.CreateUsb2Config(SelectedUsb2PortName);
                config.BaudRate = SelectedUsb2BaudRate;
                config.DataBits = SelectedUsb2DataBits;
                config.Parity = SelectedUsb2Parity;
                config.StopBits = SelectedUsb2StopBits;
                
                _serialPortManager.ConfigureUsb2Port(config);
                var success = await _serialPortManager.ConnectUsb2Async();
                
                if (success)
                {
                    SaveConfiguration();
                    MessageHelper.Success($"USB2.0串口 {SelectedUsb2PortName} 连接成功！", "连接成功");
                }
                else
                {
                    MessageHelper.Error($"USB2.0串口 {SelectedUsb2PortName} 连接失败！", "连接失败");
                }
            }
        }
        
        [RelayCommand]
        private async Task ConnectUsb3()
        {
            if (ValidateUsb3Input())
            {
                var config = SerialPortConfig.CreateUsb3Config(SelectedUsb3PortName);
                config.BaudRate = SelectedUsb3BaudRate;
                config.DataBits = SelectedUsb3DataBits;
                config.Parity = SelectedUsb3Parity;
                config.StopBits = SelectedUsb3StopBits;
                
                _serialPortManager.ConfigureUsb3Port(config);
                var success = await _serialPortManager.ConnectUsb3Async();
                
                if (success)
                {
                    SaveConfiguration();
                    MessageHelper.Success($"USB3.0串口 {SelectedUsb3PortName} 连接成功！", "连接成功");
                }
                else
                {
                    MessageHelper.Error($"USB3.0串口 {SelectedUsb3PortName} 连接失败！", "连接失败");
                }
            }
        }
        
        [RelayCommand]
        private async Task ConnectAll()
        {
            if (ValidateUsb2Input() && ValidateUsb3Input())
            {
                // 配置USB2.0
                var usb2Config = SerialPortConfig.CreateUsb2Config(SelectedUsb2PortName);
                usb2Config.BaudRate = SelectedUsb2BaudRate;
                usb2Config.DataBits = SelectedUsb2DataBits;
                usb2Config.Parity = SelectedUsb2Parity;
                usb2Config.StopBits = SelectedUsb2StopBits;
                
                // 配置USB3.0
                var usb3Config = SerialPortConfig.CreateUsb3Config(SelectedUsb3PortName);
                usb3Config.BaudRate = SelectedUsb3BaudRate;
                usb3Config.DataBits = SelectedUsb3DataBits;
                usb3Config.Parity = SelectedUsb3Parity;
                usb3Config.StopBits = SelectedUsb3StopBits;
                
                _serialPortManager.ConfigureUsb2Port(usb2Config);
                _serialPortManager.ConfigureUsb3Port(usb3Config);
                
                var (usb2Success, usb3Success) = await _serialPortManager.ConnectAllAsync();
                
                if (usb2Success && usb3Success)
                {
                    SaveConfiguration();
                    MessageHelper.Success("所有串口连接成功！", "连接成功");
                }
                else if (usb2Success)
                {
                    MessageHelper.Warning("USB2.0连接成功，USB3.0连接失败！", "部分连接成功");
                }
                else if (usb3Success)
                {
                    MessageHelper.Warning("USB3.0连接成功，USB2.0连接失败！", "部分连接成功");
                }
                else
                {
                    MessageHelper.Error("所有串口连接失败！", "连接失败");
                }
            }
        }
        
        [RelayCommand]
        private async Task DisconnectUsb2()
        {
            await _serialPortManager.DisconnectUsb2Async();
            MessageHelper.Info("USB2.0串口连接已断开。", "连接断开");
        }
        
        [RelayCommand]
        private async Task DisconnectUsb3()
        {
            await _serialPortManager.DisconnectUsb3Async();
            MessageHelper.Info("USB3.0串口连接已断开。", "连接断开");
        }
        
        [RelayCommand]
        private async Task DisconnectAll()
        {
            await _serialPortManager.DisconnectAllAsync();
            MessageHelper.Info("所有串口连接已断开。", "连接断开");
        }
        
        [RelayCommand]
        private async Task SetFilterParameter()
        {
            try
            {
                if (!_serialPortManager.IsUsb2Connected)
                {
                    MessageHelper.Warning("请先连接USB2.0串口。", "连接检查");
                    return;
                }

                var filterParameter = new FilterParameter
                {
                    AlgorithmType = SelectedFilterType
                };

                // 根据选择的滤波类型设置参数
                switch (SelectedFilterType)
                {
                    case FilterType.LowPass:
                        filterParameter.LowPassCutoffFrequency = LowPassCutoffFrequency;
                        filterParameter.LowPassCutoffFrequency = LowPassSamplingPeriod;
                        filterParameter.HighPassCutoffFrequency = 0;
                        filterParameter.HighPassSamplingPeriod = 0;
                        filterParameter.WindowWidth = 0; // 低通滤波不使用窗口宽度
                        break;
                    case FilterType.HighPass:
                        filterParameter.LowPassCutoffFrequency = 0;
                        filterParameter.LowPassCutoffFrequency = 0;
                        filterParameter.HighPassCutoffFrequency = HighPassCutoffFrequency;
                        filterParameter.HighPassSamplingPeriod = HighPassSamplingPeriod;
                        filterParameter.WindowWidth = 0; // 高通滤波不使用窗口宽度
                        break;
                    case FilterType.MovingAverage:
                        filterParameter.LowPassCutoffFrequency = 0; // 滑动平均不使用截止频率
                        filterParameter.LowPassCutoffFrequency = 0; // 滑动平均不使用采样周期
                        filterParameter.HighPassCutoffFrequency = 0;
                        filterParameter.HighPassSamplingPeriod = 0;
                        filterParameter.WindowWidth = MovingAverageWindowWidth;
                        break;
                }

                var success = await _serialPortManager.SendFilterParameterSetCommandAsync(filterParameter, true);
                
                if (success)
                {
                    MessageHelper.Success($"滤波参数设置成功！\n类型: {GetFilterTypeDisplayName(SelectedFilterType)}", "参数设置");
                }
                else
                {
                    MessageHelper.Error("滤波参数设置失败！", "参数设置");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"设置滤波参数时发生错误", ex);
            }
        }
        
        [RelayCommand]
        private async Task SetDelayParameter()
        {
            try
            {
                if (!_serialPortManager.IsUsb2Connected)
                {
                    MessageHelper.Warning("请先连接USB2.0串口。", "连接检查");
                    return;
                }

                var delayParameter = new DelayParameter
                {
                    DelayTime = SignalDelayTime
                };

                var success = await _serialPortManager.SendDelayParameterSetCommandAsync(delayParameter, true);
                
                if (success)
                {
                    MessageHelper.Success($"信号时延参数设置成功！\n延迟时长: {SignalDelayTime} ms", "参数设置");
                }
                else
                {
                    MessageHelper.Error("信号时延参数设置失败！", "参数设置");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"设置信号时延参数时发生错误", ex);
            }
        }
        
        [RelayCommand]
        private async Task SetSystemParameter()
        {
            try
            {
                if (!_serialPortManager.IsUsb2Connected)
                {
                    MessageHelper.Warning("请先连接USB2.0串口。", "连接检查");
                    return;
                }

                var systemParameter = new SystemParameter
                {
                    ChannelSelection = SelectedSystemParameterChannel,
                    GainAdjustment = GainAdjustment,
                    BaselineAdjustment = BaselineAdjustment
                };

                var success = await _serialPortManager.SendSystemParameterSetCommandAsync(systemParameter, true);
                
                if (success)
                {
                    MessageHelper.Success($"系统参数设置成功！\n通道: {SelectedSystemParameterChannel + 1}\n增益: {GainAdjustment} mv\n基线: {BaselineAdjustment} mv", "参数设置");
                }
                else
                {
                    MessageHelper.Error("系统参数设置失败！", "参数设置");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"设置系统参数时发生错误", ex);
            }
        }

        [RelayCommand]
        private async Task SetOscilloscopeParameter()
        {
            try
            {
                if (!_serialPortManager.IsUsb2Connected)
                {
                    MessageHelper.Warning("请先连接USB2.0串口。", "连接检查");
                    return;
                }

                var oscilloscopeParameter = new OscilloscopeControlParameter
                {
                    OscilloscopeControl = (ushort)(IsOscilloscopeEnabled ? 1 : 0),
                    Channel0Address = OscilloscopeChannels[0].ParameterAddress,
                    Channel1Address = OscilloscopeChannels[1].ParameterAddress,
                    Channel2Address = OscilloscopeChannels[2].ParameterAddress,
                    Channel3Address = OscilloscopeChannels[3].ParameterAddress,
                    Channel4Address = OscilloscopeChannels[4].ParameterAddress,
                    Channel5Address = OscilloscopeChannels[5].ParameterAddress,
                    Channel6Address = OscilloscopeChannels[6].ParameterAddress,
                    Channel7Address = OscilloscopeChannels[7].ParameterAddress
                };

                var success = await _serialPortManager.SendOscilloscopeParameterSetCommandAsync(oscilloscopeParameter, true);

                if (success)
                {
                    var enabledChannels = OscilloscopeChannels.Where(c => c.IsEnabled).Count();
                    MessageHelper.Success($"示波器参数设置成功！\n状态: {(IsOscilloscopeEnabled ? "启用" : "禁用")}\n启用通道数: {enabledChannels}", "参数设置");

                    // 发送示波器配置更新消息给AnalyzerViewModel
                    WeakReferenceMessenger.Default.Send(new OscilloscopeConfigurationMessage(oscilloscopeParameter));
                }
                else
                {
                    MessageHelper.Error("示波器参数设置失败！", "参数设置");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"设置示波器参数时发生错误", ex);
            }
        }
        
        [RelayCommand]
        private void ResetOscilloscopeChannels()
        {
            foreach (var channel in OscilloscopeChannels)
            {
                channel.IsEnabled = false;
                channel.GainAdjustment = 1000;
                channel.BaselineAdjustment = 0;
                channel.DataType = OscilloscopeDataType.OriginalData; // 设置数据类型会自动更新参数地址和描述
                // ParameterAddress 和 Description 会在设置 DataType 时自动更新
            }

            MessageHelper.Info("示波器通道配置已重置。", "配置重置");
        }
        
        private void InitializeOscilloscopeChannels()
        {
            OscilloscopeChannels.Clear();

            for (int i = 0; i < 8; i++)
            {
                var channel = new OscilloscopeChannelConfig
                {
                    ChannelNumber = i,
                    ChannelName = $"通道 {i + 1}",
                    IsEnabled = false,
                    DataType = OscilloscopeDataType.OriginalData, // 设置数据类型会自动计算参数地址
                    GainAdjustment = 1000,
                    BaselineAdjustment = 0
                    // ParameterAddress 和 Description 会在设置 DataType 时自动更新
                };

                OscilloscopeChannels.Add(channel);
            }
        }
        
        private string GetFilterTypeDisplayName(FilterType filterType)
        {
            return filterType switch
            {
                FilterType.LowPass => "低通滤波",
                FilterType.HighPass => "高通滤波",
                FilterType.MovingAverage => "滑动平均",
                _ => "未知类型"
            };
        }
        
        private void OnUsb2ConnectionStatusChanged(bool isConnected)
        {
            IsUsb2Connected = isConnected;
        }
        
        private void OnUsb3ConnectionStatusChanged(bool isConnected)
        {
            IsUsb3Connected = isConnected;
        }
        
        private bool ValidateUsb2Input()
        {
            if (string.IsNullOrEmpty(SelectedUsb2PortName))
            {
                MessageHelper.Warning("请选择USB2.0串口。", "输入验证");
                return false;
            }
            
            if (SelectedUsb2BaudRate <= 0)
            {
                MessageHelper.Warning("请输入有效的USB2.0波特率。", "输入验证");
                return false;
            }
            
            if (SelectedUsb2DataBits < 5 || SelectedUsb2DataBits > 8)
            {
                MessageHelper.Warning("USB2.0数据位必须在5-8之间。", "输入验证");
                return false;
            }
            
            return true;
        }
        
        private bool ValidateUsb3Input()
        {
            if (string.IsNullOrEmpty(SelectedUsb3PortName))
            {
                MessageHelper.Warning("请选择USB3.0串口。", "输入验证");
                return false;
            }
            
            if (SelectedUsb3BaudRate <= 0)
            {
                MessageHelper.Warning("请输入有效的USB3.0波特率。", "输入验证");
                return false;
            }
            
            if (SelectedUsb3DataBits < 5 || SelectedUsb3DataBits > 8)
            {
                MessageHelper.Warning("USB3.0数据位必须在5-8之间。", "输入验证");
                return false;
            }
            
            if (SelectedUsb2PortName == SelectedUsb3PortName)
            {
                MessageHelper.Warning("USB2.0和USB3.0不能使用相同的串口。", "输入验证");
                return false;
            }
            
            return true;
        }
        
        private void SaveConfiguration()
        {
            try
            {
                var configs = new Dictionary<string, SerialPortConfig>
                {
                    ["usb2"] = new SerialPortConfig
                    {
                        PortName = SelectedUsb2PortName,
                        BaudRate = SelectedUsb2BaudRate,
                        DataBits = SelectedUsb2DataBits,
                        Parity = SelectedUsb2Parity,
                        StopBits = SelectedUsb2StopBits,
                        PortType = SerialPortType.Usb2
                    },
                    ["usb3"] = new SerialPortConfig
                    {
                        PortName = SelectedUsb3PortName,
                        BaudRate = SelectedUsb3BaudRate,
                        DataBits = SelectedUsb3DataBits,
                        Parity = SelectedUsb3Parity,
                        StopBits = SelectedUsb3StopBits,
                        PortType = SerialPortType.Usb3
                    }
                };
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    Converters = { new JsonStringEnumConverter() }
                };
                
                var jsonString = JsonSerializer.Serialize(configs, options);
                File.WriteAllText(_configFilePath, jsonString);
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"保存配置文件失败", ex);
            }
        }

        #region 参数上报消息接收

        /// <summary>
        /// 接收系统参数上报
        /// </summary>
        public void Receive(SystemParameterReceivedMessage message)
        {
            ReceivedSystemParameter = message.Parameter;
            
            // 自动更新UI绑定的数据
            SelectedSystemParameterChannel = message.Parameter.ChannelSelection;
            GainAdjustment = message.Parameter.GainAdjustment;
            BaselineAdjustment = (ushort)message.Parameter.BaselineAdjustment;
            
            LastParameterUpdateInfo = $"系统参数上报 - {DateTime.Now:HH:mm:ss} - 通道: {message.Parameter.ChannelSelection + 1}, 增益: {message.Parameter.GainAdjustment}mv, 基线: {message.Parameter.BaselineAdjustment}mv";
        }

        /// <summary>
        /// 接收滤波参数上报
        /// </summary>
        public void Receive(FilterParameterReceivedMessage message)
        {
            ReceivedFilterParameter = message.Parameter;
            
            // 自动更新UI绑定的数据
            SelectedFilterType = message.Parameter.AlgorithmType;
            LowPassCutoffFrequency = message.Parameter.LowPassCutoffFrequency;
            LowPassSamplingPeriod = message.Parameter.LowPassSamplingPeriod;
            HighPassCutoffFrequency = message.Parameter.HighPassCutoffFrequency;
            HighPassSamplingPeriod = message.Parameter.HighPassSamplingPeriod;
            MovingAverageWindowWidth = message.Parameter.WindowWidth;
            
            LastParameterUpdateInfo = $"滤波参数上报 - {DateTime.Now:HH:mm:ss} - 算法: {GetFilterTypeDisplayName(message.Parameter.AlgorithmType)}";
        }

        /// <summary>
        /// 接收信号时延参数上报
        /// </summary>
        public void Receive(DelayParameterReceivedMessage message)
        {
            ReceivedDelayParameter = message.Parameter;
            
            // 自动更新UI绑定的数据
            SignalDelayTime = message.Parameter.DelayTime;
            
            LastParameterUpdateInfo = $"时延参数上报 - {DateTime.Now:HH:mm:ss} - 延迟: {message.Parameter.DelayTime}ms";
        }

        

        /// <summary>
        /// 清理资源并取消消息注册
        /// </summary>
        public void Cleanup()
        {
            WeakReferenceMessenger.Default.UnregisterAll(this);
        }

        #endregion
    }
}