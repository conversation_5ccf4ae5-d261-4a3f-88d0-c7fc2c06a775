using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using ScottPlot;
using ScottPlot.WPF;
using GaugeCtrl.Communication;
using GaugeCtrl.Communication.Enums;
using GaugeCtrl.Communication.Messages;
using GaugeCtrl.Communication.Models;
using GaugeCtrl.Communication.RequestInfos;
using GaugeCtrl.Helpers;
using ScottPlot.Plottables;

namespace GaugeCtrl.ViewModels
{
    public partial class AnalyzerViewModel : ObservableObject,
        IRecipient<SystemParameterReceivedMessage>,
        IRecipient<ThresholdParameterReceivedMessage>,
        IRecipient<OscilloscopeConfigurationMessage>
    {
        #region 属性

        // 通道选项
        public ObservableCollection<string> ChannelOptions { get; } =
            ["CH0", "CH1", "CH2", "CH3", "CH4", "CH5", "CH6", "CH7"];

        // 数据类型选项
        public ObservableCollection<string> DataTypeOptions { get; } =
        [
            "ADC采样后原始数据",
            "降噪滤波、减直流后数据",
            "位宽扩展后数据"
        ];

        // 显示类型选项


        [ObservableProperty] private string selectedTriggerChannel = "CH0";

        [ObservableProperty] private uint triggerThreshold = 2048;

        [ObservableProperty] private string selectedDataType = "ADC采样后原始数据";

        [ObservableProperty] private string selectedChannel1 = "CH0";

        [ObservableProperty] private string selectedChannel2 = "CH1";

        [ObservableProperty] private string selectedChannel3 = "CH2";

        [ObservableProperty] private string selectedChannel4 = "CH3";

        // AnalyzerView页面的4个Plot通道选择
        [ObservableProperty] private string selectedPlot1Channel = "CH0";

        [ObservableProperty] private string selectedPlot2Channel = "CH1";

        [ObservableProperty] private string selectedPlot3Channel = "CH2";

        [ObservableProperty] private string selectedPlot4Channel = "CH3";

        [ObservableProperty] private string selectedChannel = "CH0";

        [ObservableProperty] private int vadjValue = 0;

        [ObservableProperty] private int voffsetValue = 0;

        [ObservableProperty] private int _sampleIntervalMilliseconds = 1;

        # region 信号提取参数上报

        public ObservableCollection<string> DisplayTypeOptions { get; } = ["A", "H", "W"];

        [ObservableProperty] private int selectedCountsChannel = 0;

        [ObservableProperty] private string selectedDisplayType = "A";

        /// <summary>
        /// 信号提取参数缓存
        /// </summary>
        private readonly List<SignalExtractionParams> _countPlotCache = [];

        private readonly DispatcherTimer _countPlotDisplayTimer = new();

        [ObservableProperty] private bool _countPlotIsCollecting;

        // 用于记录已读取的数据索引
        private int _countPlotLastReadIndex = 0;

        [ObservableProperty] private WpfPlot? _countWpfPlot;

        private Plot? _countPlot;

        private DataLogger? _countPlotDataLogger;

        #endregion

        #region 示波器数据处理

        /// <summary>
        /// 示波器数据缓存
        /// </summary>
        private readonly List<OscilloscopeDataReport> _oscilloscopeDataCache = [];

        private readonly DispatcherTimer _oscilloscopeDisplayTimer = new();

        [ObservableProperty] private bool _oscilloscopeIsCollecting;

        // 用于记录已读取的示波器数据索引
        private int _oscilloscopeLastReadIndex = 0;

        // 4个通道的Plot控件
        [ObservableProperty] private WpfPlot? _plot1WpfPlot;
        [ObservableProperty] private WpfPlot? _plot2WpfPlot;
        [ObservableProperty] private WpfPlot? _plot3WpfPlot;
        [ObservableProperty] private WpfPlot? _plot4WpfPlot;

        private Plot? _plot1;
        private Plot? _plot2;
        private Plot? _plot3;
        private Plot? _plot4;

        private DataLogger? _plot1DataLogger;
        private DataLogger? _plot2DataLogger;
        private DataLogger? _plot3DataLogger;
        private DataLogger? _plot4DataLogger;

        /// <summary>
        /// 当前数据类型参数
        /// </summary>
        private RespTypeParameter? _currentRespTypeParameter;

        /// <summary>
        /// 当前示波器配置参数（来自SerialSettingsView）
        /// </summary>
        private OscilloscopeControlParameter? _currentOscilloscopeConfiguration;

        #endregion


        [ObservableProperty] private bool usb2Status;

        [ObservableProperty] private bool usb3Status;

        [ObservableProperty] private bool isCollecting = false;

        [ObservableProperty] private bool isSavingRawData = false;

        [ObservableProperty] private bool isSavingProcessedData = false;

        #endregion

        #region 数据结构

        // ADC数据结构
        public class AdcData
        {
            public int Channel { get; set; }
            public int Value { get; set; }
            public ulong Timestamp { get; set; }
            public DataProcessingType ProcessingType { get; set; }
        }

        // 数据处理类型
        public enum DataProcessingType
        {
            Raw, // 原始数据
            Filtered, // 滤波后数据
            Extended // 位宽扩展后数据
        }

        // 统计数据结构
        public class CountsData
        {
            public int Channel { get; set; }
            public string Type { get; set; } = string.Empty; // A, H, W
            public double Value { get; set; }
            public DateTime Timestamp { get; set; }
        }

        #endregion

        #region 数据存储

        private readonly List<AdcData> _rawDataBuffer = [];
        private readonly List<AdcData> _processedDataBuffer = [];
        private readonly List<CountsData> _countsDataBuffer = [];

        #endregion

        #region 命令

        [RelayCommand]
        private async Task SaveRawData()
        {
            if (IsSavingRawData)
                return;

            IsSavingRawData = true;

            try
            {
                var fileName = $"RawData_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                await SaveDataToCsv(_rawDataBuffer, fileName);
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"保存原始数据失败", ex);
            }
            finally
            {
                IsSavingRawData = false;
            }
        }

        [RelayCommand]
        private void StopSaveRawData()
        {
            IsSavingRawData = false;
        }

        [RelayCommand]
        private async Task SaveProcessedData()
        {
            if (IsSavingProcessedData)
                return;

            IsSavingProcessedData = true;

            try
            {
                var fileName = $"ProcessedData_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                await SaveDataToCsv(_processedDataBuffer, fileName);
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"保存处理后数据失败", ex);
            }
            finally
            {
                IsSavingProcessedData = false;
            }
        }

        [RelayCommand]
        private void StopSaveProcessedData()
        {
            IsSavingProcessedData = false;
        }

        [RelayCommand]
        private async Task StartCollection()
        {
            if (IsCollecting || _serialPortManager == null)
                return;

            try
            {
                // 检查连接状态
                if (!_serialPortManager.IsBothConnected)
                {
                    MessageHelper.Warning("请先连接USB2.0和USB3.0串口");
                    return;
                }

                // 发送设备控制命令开始采集
                var success = await _serialPortManager.SendDeviceControlCommandAsync(DeviceControlCommand.Start);

                if (success)
                {
                    IsCollecting = true;
                    UpdateConnectionStatus();
                }
                else
                {
                    MessageHelper.Error("发送开始采集命令失败");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"开始采集失败", ex);
            }
        }

        [RelayCommand]
        private async Task StopCollection()
        {
            if (!IsCollecting || _serialPortManager == null)
                return;

            try
            {
                // 发送设备控制命令停止采集
                var success = await _serialPortManager.SendDeviceControlCommandAsync(DeviceControlCommand.Stop);

                if (success)
                {
                    IsCollecting = false;
                    UpdateConnectionStatus();
                }
                else
                {
                    MessageHelper.Error("发送停止采集命令失败");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"停止采集失败", ex);
            }
        }

        [RelayCommand]
        private async Task ReadSystemParam()
        {
            try
            {
                if (_serialPortManager == null || !_serialPortManager.IsUsb2Connected)
                {
                    MessageHelper.Warning("请先连接USB2.0串口。", "连接检查");
                    return;
                }

                var channelNum = GetChannelNumber(SelectedChannel);

                // 发送设备信息请求来读取参数
                await _serialPortManager.SendDeviceInfoRequestAsync(1, ParameterType.SystemParameter);

                MessageHelper.Info($"正在读取通道{SelectedChannel}的系统参数...", "系统参数");
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"读取系统参数失败", ex);
            }
        }

        [RelayCommand]
        private async Task SetSystemParam()
        {
            try
            {
                if (_serialPortManager == null || !_serialPortManager.IsUsb2Connected)
                {
                    MessageHelper.Warning("请先连接USB2.0串口。", "连接检查");
                    return;
                }

                var channelNum = GetChannelNumber(SelectedChannel);

                // 创建系统参数，同时设置Vadj和Voffset
                var systemParam = new SystemParameter
                {
                    ChannelSelection = (ushort)channelNum,
                    GainAdjustment = (ushort)VadjValue,
                    BaselineAdjustment = (uint)VoffsetValue
                };

                var success = await _serialPortManager.SendSystemParameterSetCommandAsync(systemParam);

                if (success)
                {
                    MessageHelper.Success(
                        $"系统参数设置成功！\n通道: {SelectedChannel}\nVadj: {VadjValue}\nVoffset: {VoffsetValue}", "参数设置");
                }
                else
                {
                    MessageHelper.Error("系统参数设置失败！", "参数设置");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"设置系统参数失败", ex);
            }
        }

        [RelayCommand]
        private async Task SetTriggerThreshold()
        {
            if (_serialPortManager == null || !_serialPortManager.IsUsb2Connected)
            {
                MessageHelper.Warning("请先连接USB2.0串口");
                return;
            }

            try
            {
                var channelNum = GetChannelNumber(SelectedTriggerChannel);

                // 创建阈值参数
                var thresholdParam = new ThresholdParameter
                {
                    BitWidthExpandThreshold = (uint)TriggerThreshold
                };

                await _serialPortManager.SendThresholdParameterSetCommandAsync(thresholdParam);
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"设置触发阈值失败", ex);
            }
        }

        [RelayCommand]
        private async Task SetDataType()
        {
            if (_serialPortManager == null || !_serialPortManager.IsUsb2Connected)
            {
                MessageHelper.Warning("请先连接USB2.0串口");
                return;
            }

            try
            {
                // 创建数据类型参数
                var dataTypeParam = new RespTypeParameter
                {
                    UploadType = GetDataTypeCode(SelectedDataType)
                };

                await _serialPortManager.SendDataTypeParameterSetCommandAsync(dataTypeParam);
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"设置数据类型失败", ex);
            }
        }

        #endregion

        #region 辅助方法

        public void InitializeCountWpfPlot(WpfPlot wpfPlot)
        {
            CountWpfPlot = wpfPlot;
            _countPlot = CountWpfPlot.Plot;
            CountWpfPlot.Plot.Clear();

            // 设置轴标签
            CountWpfPlot.Plot.Axes.Bottom.Label.Text = "AD量化字";
            CountWpfPlot.Plot.Axes.Left.Label.Text = "计数";
            //中文字体自适应
            CountWpfPlot.Plot.Font.Automatic();
            // 设置坐标范围
            CountWpfPlot.Plot.Axes.SetLimits(1, 4096, 1, 10000);

            // 添加示例数据
            // AddSampleCountsData();

            CountWpfPlot.Refresh();
        }

        public void InitializeOscilloscopePlots(WpfPlot plot1, WpfPlot plot2, WpfPlot plot3, WpfPlot plot4)
        {
            // 初始化Plot1
            Plot1WpfPlot = plot1;
            _plot1 = Plot1WpfPlot.Plot;
            _plot1.Clear();
            _plot1.Axes.Bottom.Label.Text = "";
            _plot1.Axes.Left.Label.Text = $"{SelectedPlot1Channel}";
            _plot1.Font.Automatic();
            _plot1.Axes.SetLimits(0, 1000, 0, 4095);
            _plot1DataLogger = _plot1.Add.DataLogger();

            // 初始化Plot2
            Plot2WpfPlot = plot2;
            _plot2 = Plot2WpfPlot.Plot;
            _plot2.Clear();
            _plot2.Axes.Bottom.Label.Text = "";
            _plot2.Axes.Left.Label.Text = $"{SelectedPlot2Channel}";
            _plot2.Font.Automatic();
            _plot2.Axes.SetLimits(0, 1000, 0, 4095);
            _plot2DataLogger = _plot2.Add.DataLogger();

            // 初始化Plot3
            Plot3WpfPlot = plot3;
            _plot3 = Plot3WpfPlot.Plot;
            _plot3.Clear();
            _plot3.Axes.Bottom.Label.Text = "";
            _plot3.Axes.Left.Label.Text = $"{SelectedPlot3Channel}";
            _plot3.Font.Automatic();
            _plot3.Axes.SetLimits(0, 1000, 0, 4095);
            _plot3DataLogger = _plot3.Add.DataLogger();

            // 初始化Plot4
            Plot4WpfPlot = plot4;
            _plot4 = Plot4WpfPlot.Plot;
            _plot4.Clear();
            _plot4.Axes.Bottom.Label.Text = "时间 (ms)";
            _plot4.Axes.Left.Label.Text = $"{SelectedPlot4Channel}";
            _plot4.Font.Automatic();
            _plot4.Axes.SetLimits(0, 1000, 0, 4095);
            _plot4DataLogger = _plot4.Add.DataLogger();

            // 刷新所有Plot
            Plot1WpfPlot.Refresh();
            Plot2WpfPlot.Refresh();
            Plot3WpfPlot.Refresh();
            Plot4WpfPlot.Refresh();
        }

        private int GetChannelNumber(string channelName)
        {
            return int.Parse(channelName.Substring(2)); // 从"CH0"中提取"0"
        }

        private DataUploadType GetDataTypeCode(string dataType)
        {
            return dataType switch
            {
                "ADC采样后原始数据" => DataUploadType.RawData,
                "降噪滤波、减直流后数据" => DataUploadType.BaselineRemovedData,
                "位宽扩展后数据" => DataUploadType.BitWidthExpandedData,
                _ => DataUploadType.RawData
            };
        }

        private DataProcessingType GetProcessingType(string dataType)
        {
            return dataType switch
            {
                "ADC采样后原始数据" => DataProcessingType.Raw,
                "降噪滤波、减直流后数据" => DataProcessingType.Filtered,
                "位宽扩展后数据" => DataProcessingType.Extended,
                _ => DataProcessingType.Raw
            };
        }

        private OscilloscopeDataType GetExpectedOscilloscopeDataType(string dataType)
        {
            return dataType switch
            {
                "ADC采样后原始数据" => OscilloscopeDataType.OriginalData,
                "降噪滤波、减直流后数据" => OscilloscopeDataType.BaselineData,
                "位宽扩展后数据" => OscilloscopeDataType.BitWidthData,
                _ => OscilloscopeDataType.OriginalData
            };
        }

        private async Task SaveDataToCsv(List<AdcData> data, string fileName)
        {
            var csv = new StringBuilder();
            csv.AppendLine("Timestamp,Channel,Value,ProcessingType");

            foreach (var item in data)
            {
                csv.AppendLine(
                    $"{item.Timestamp:yyyy-MM-dd HH:mm:ss.fff},{item.Channel},{item.Value},{item.ProcessingType}");
            }

            await File.WriteAllTextAsync(fileName, csv.ToString());
        }

        #endregion

        #region 串口管理器和事件处理

        private readonly SerialPortManager? _serialPortManager;

        public AnalyzerViewModel(SerialPortManager? serialPortManager = null)
        {
            _serialPortManager = serialPortManager;

            // 订阅串口管理器事件
            if (_serialPortManager != null)
            {
                _serialPortManager.Usb2ConnectionStatusChanged += OnUsb2ConnectionStatusChanged;
                _serialPortManager.Usb3ConnectionStatusChanged += OnUsb3ConnectionStatusChanged;

                _serialPortManager.SetUsb2EventHandlers(
                    OnSignalExtractionParamsReceived,
                    OnOscilloscopeDataReceived,
                    OnRespTypeParameterReceived);


                UpdateConnectionStatus();
            }

            // 100ms间隔的显示定时器
            _countPlotDisplayTimer.Tick += UpdateCountPlotDisplay;
            _countPlotDisplayTimer.Interval = TimeSpan.FromMilliseconds(100);

            // 50ms间隔的示波器显示定时器
            _oscilloscopeDisplayTimer.Tick += UpdateOscilloscopeDisplay;
            _oscilloscopeDisplayTimer.Interval = TimeSpan.FromMilliseconds(50);

            // 注册消息接收
            WeakReferenceMessenger.Default.Register<SystemParameterReceivedMessage>(this);
            WeakReferenceMessenger.Default.Register<ThresholdParameterReceivedMessage>(this);
            WeakReferenceMessenger.Default.Register<OscilloscopeConfigurationMessage>(this);
        }

        private void OnUsb2ConnectionStatusChanged(bool isConnected)
        {
            UpdateConnectionStatus();
        }

        private void OnUsb3ConnectionStatusChanged(bool isConnected)
        {
            UpdateConnectionStatus();
        }

        private void UpdateConnectionStatus()
        {
            // 更新USB2.0状态
            Usb2Status = _serialPortManager.IsUsb2Connected;

            // 更新USB3.0状态
            Usb3Status = _serialPortManager.IsUsb3Connected;
        }

        private void OnOscilloscopeDataReceived(OscilloscopeDataReport data)
        {
            // 检查是否有当前数据类型参数配置
            if (_currentRespTypeParameter == null)
                return;

            // 检查数据类型是否匹配页面选择的数据类型
            var expectedDataType = GetExpectedOscilloscopeDataType(SelectedDataType);
            if (data.DataType != expectedDataType)
                return;

            // 添加到缓存
            _oscilloscopeDataCache.Add(data);
        }

        private void OnSignalExtractionParamsReceived(SignalExtractionParams channels)
        {
            _countPlotCache.Add(channels);
        }

        private void OnRespTypeParameterReceived(RespTypeParameter parameter)
        {
            _currentRespTypeParameter = parameter;
        }

        [RelayCommand]
        private async Task CountPlotStart()
        {
            if (!_serialPortManager.IsUsb2Connected)
            {
                MessageHelper.Warning("USB2.0串口未连接");
                return;
            }

            if (!CountPlotIsCollecting)
            {
                CountPlotIsCollecting = true;
                _countPlotLastReadIndex = 0; // 重置读取索引


                // 清空DataLogger
                CountWpfPlot.Plot?.Clear();

                // 启动显示定时器 - 50ms间隔
                _countPlotDisplayTimer.Start();

                // 更新十字线可见性（运行时隐藏）
                // UpdateCrosshairVisibility();

                await _serialPortManager.SendSignalExtractionParameterSetCommandAsync(true);
            }
        }

        [RelayCommand]
        private void CountPlotStop()
        {
            if (!_serialPortManager.IsUsb2Connected)
            {
                MessageHelper.Warning("USB2.0串口未连接");
                return;
            }

            if (CountPlotIsCollecting)
            {
                CountPlotIsCollecting = false;
                _countPlotDisplayTimer.Stop();

                // 停止周期性请求
                _serialPortManager.SendSignalExtractionParameterSetCommandAsync(false);


                // 更新十字线可见性（停止时显示）
                // UpdateCrosshairVisibility();

                // 停止时将缓存数据用Signal填充到Plot
                FillCountPlotWithSignal();
            }
        }

        [RelayCommand]
        private async Task OscilloscopeStart()
        {
            if (!_serialPortManager.IsUsb2Connected)
            {
                MessageHelper.Warning("USB2.0串口未连接");
                return;
            }

            if (!OscilloscopeIsCollecting)
            {
                OscilloscopeIsCollecting = true;
                _oscilloscopeLastReadIndex = 0; // 重置读取索引

                // 清空所有Plot的DataLogger
                _plot1DataLogger?.Clear();
                _plot2DataLogger?.Clear();
                _plot3DataLogger?.Clear();
                _plot4DataLogger?.Clear();

                // 启动显示定时器 - 50ms间隔
                _oscilloscopeDisplayTimer.Start();

                // 发送示波器控制命令 - 使用SerialSettingsView中的示波器配置
                if (_currentOscilloscopeConfiguration != null)
                {
                    // 使用已配置的示波器参数，只修改控制位
                    var oscilloscopeParam = new OscilloscopeControlParameter
                    {
                        OscilloscopeControl = 1,
                        Channel0Address = _currentOscilloscopeConfiguration.Channel0Address,
                        Channel1Address = _currentOscilloscopeConfiguration.Channel1Address,
                        Channel2Address = _currentOscilloscopeConfiguration.Channel2Address,
                        Channel3Address = _currentOscilloscopeConfiguration.Channel3Address,
                        Channel4Address = _currentOscilloscopeConfiguration.Channel4Address,
                        Channel5Address = _currentOscilloscopeConfiguration.Channel5Address,
                        Channel6Address = _currentOscilloscopeConfiguration.Channel6Address,
                        Channel7Address = _currentOscilloscopeConfiguration.Channel7Address
                    };

                    await _serialPortManager.SendOscilloscopeParameterSetCommandAsync(oscilloscopeParam);
                }
                else
                {
                    MessageHelper.Warning("请先在串口设置页面配置示波器参数");
                    OscilloscopeIsCollecting = false;
                    _oscilloscopeDisplayTimer.Stop();
                    return;
                }
            }
        }

        [RelayCommand]
        private async Task OscilloscopeStop()
        {
            if (OscilloscopeIsCollecting)
            {
                OscilloscopeIsCollecting = false;

                // 停止显示定时器
                _oscilloscopeDisplayTimer.Stop();

                // 发送停止示波器控制命令
                var oscilloscopeParam = new OscilloscopeControlParameter
                {
                    OscilloscopeControl = 0
                };

                await _serialPortManager.SendOscilloscopeParameterSetCommandAsync(oscilloscopeParam);
            }
        }

        // MainWindow.xaml 中使用的命令别名
        [RelayCommand]
        private async Task StartCollection()
        {
            await OscilloscopeStart();
        }

        [RelayCommand]
        private async Task StopCollection()
        {
            await OscilloscopeStop();
        }

        private void UpdateCountPlotDisplay(object sender, EventArgs e)
        {
            // 检查是否有足够的新数据（至少100条）
            var availableDataCount = _countPlotCache.Count - _countPlotLastReadIndex;
            if (availableDataCount < 100)
                return; // 不满100条则等待

            // 读取100条数据
            var batchSize = 100;
            var endIndex = _countPlotLastReadIndex + batchSize;

            for (int i = _countPlotLastReadIndex;
                 i < endIndex && i < _countPlotCache.Count;
                 i++)
            {
                switch (selectedDisplayType)
                {
                    case "A":
                        _countPlotDataLogger.Add(i, _countPlotCache[i].Channels[SelectedCountsChannel].SignalArea);
                        break;
                    case "H":
                        _countPlotDataLogger.Add(i, _countPlotCache[i].Channels[SelectedCountsChannel].SignalHeight);
                        break;
                    case "W":
                        _countPlotDataLogger.Add(i, _countPlotCache[i].Channels[SelectedCountsChannel].SignalWidth);
                        break;
                }
            }

            // 更新已读取的索引
            _countPlotLastReadIndex = endIndex;

            CountWpfPlot?.Refresh();
        }

        private void UpdateOscilloscopeDisplay(object sender, EventArgs e)
        {
            // 检查是否有足够的新数据（至少50条）
            var availableDataCount = _oscilloscopeDataCache.Count - _oscilloscopeLastReadIndex;
            if (availableDataCount < 50)
                return; // 不满50条则等待

            // 检查是否有数据类型参数配置
            if (_currentRespTypeParameter == null)
                return;

            // 读取50条数据
            var batchSize = 50;
            var endIndex = _oscilloscopeLastReadIndex + batchSize;

            for (int i = _oscilloscopeLastReadIndex; i < endIndex && i < _oscilloscopeDataCache.Count; i++)
            {
                var data = _oscilloscopeDataCache[i];

                // 根据AnalyzerView页面的4个通道设置来显示对应的通道数据
                UpdateChannelPlot(0, data, GetChannelNumber(SelectedPlot1Channel));
                UpdateChannelPlot(1, data, GetChannelNumber(SelectedPlot2Channel));
                UpdateChannelPlot(2, data, GetChannelNumber(SelectedPlot3Channel));
                UpdateChannelPlot(3, data, GetChannelNumber(SelectedPlot4Channel));
            }

            // 更新已读取的索引
            _oscilloscopeLastReadIndex = endIndex;

            // 刷新所有Plot
            Plot1WpfPlot?.Refresh();
            Plot2WpfPlot?.Refresh();
            Plot3WpfPlot?.Refresh();
            Plot4WpfPlot?.Refresh();
        }

        private void UpdateChannelPlot(int plotIndex, OscilloscopeDataReport data, int channelIndex)
        {
            if (channelIndex < 0 || channelIndex >= data.Channels.Length)
                return;

            DataLogger? dataLogger = plotIndex switch
            {
                0 => _plot1DataLogger,
                1 => _plot2DataLogger,
                2 => _plot3DataLogger,
                3 => _plot4DataLogger,
                _ => null
            };

            if (dataLogger != null)
            {
                var timestamp = _oscilloscopeLastReadIndex + (plotIndex * 50);
                dataLogger.Add(timestamp, data.Channels[channelIndex]);
            }
        }

        private void FillCountPlotWithSignal()
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                try
                {
                    // 使用DisplayData数组进行绘制（保持不变）
                    if (_countPlotCache.Count == 0) return;

                    // 只清除DataLogger，保留十字星
                    _countPlotDataLogger?.Clear();

                    // 移除DataLogger从Plot中
                    if (_countPlotDataLogger != null) _countPlot?.Remove(_countPlotDataLogger);

                    // 使用Signal填充Plot
                    _countPlot?.Add.Signal(_countPlotCache.Take(_countPlotCache.Count).ToArray(),
                        SampleIntervalMilliseconds);
                    var signal1 = selectedDisplayType switch
                    {
                        "A" => _countPlot?.Add.Signal(
                            _countPlotCache.Select(x => x.Channels[SelectedCountsChannel].SignalArea).ToArray(),
                            SampleIntervalMilliseconds),
                        "H" => _countPlot?.Add.Signal(
                            _countPlotCache.Select(x => x.Channels[SelectedCountsChannel].SignalHeight).ToArray(),
                            SampleIntervalMilliseconds),
                        "W" => _countPlot?.Add.Signal(
                            _countPlotCache.Select(x => x.Channels[SelectedCountsChannel].SignalWidth).ToArray(),
                            SampleIntervalMilliseconds),
                    };

                    if (signal1 != null)
                    {
                        signal1.Color = Colors.Blue;
                        signal1.LineWidth = 1;
                        signal1.LegendText = selectedDisplayType;
                    }

                    // 自动缩放
                    _countPlot?.Axes.AutoScale();

                    // 确保十字星可见性正确
                    // UpdateCrosshairVisibility();

                    CountWpfPlot?.Refresh();
                }
                catch (Exception ex)
                {
                    MessageHelper.Error("填充图表时发生错误", ex);
                }
            });
        }

       
        

        #endregion


        /// <summary>
        /// 接收触发阈值参数上报
        /// </summary>
        public void Receive(ThresholdParameterReceivedMessage message)
        {
            var isEnabled = message.Parameter.ThresholdMechanismOperation == 1 ? true : false;
            TriggerThreshold = message.Parameter.BitWidthExpandThreshold;
        }

        /// <summary>
        /// 接收系统参数上报
        /// </summary>
        public void Receive(SystemParameterReceivedMessage message)
        {
            // 自动更新UI绑定的数据
            SelectedChannel = message.Parameter.ChannelSelection.ToString();
            VadjValue = message.Parameter.GainAdjustment;
            VoffsetValue = (ushort)message.Parameter.BaselineAdjustment;
        }

        /// <summary>
        /// 接收示波器配置更新消息
        /// </summary>
        public void Receive(OscilloscopeConfigurationMessage message)
        {
            _currentOscilloscopeConfiguration = message.Configuration;
        }
    }
}